# 🚀 RAG Legal Chatbot - Quick Start Guide

## Phase 1 Complete! Ready for Development

### 📋 Prerequisites Checklist
- [x] Python 3.9+ installed
- [x] Docker and Docker Compose installed
- [x] Virtual environment created
- [x] Dependencies installed
- [x] Project structure established

### 🔧 Environment Setup

1. **Configure Environment Variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your API keys:
   # - GOOGLE_API_KEY=your_google_ai_api_key
   # - COHERE_API_KEY=your_cohere_api_key
   # - SECRET_KEY=your_secret_key
   ```

2. **Activate Virtual Environment**
   ```bash
   source venv/bin/activate
   ```

### 🐳 Docker Services

**Start Development Environment:**
```bash
# Start all services (Milvus, Redis, etc.)
docker-compose -f docker-compose.dev.yml up -d

# Check service status
docker-compose -f docker-compose.dev.yml ps

# View logs
docker-compose -f docker-compose.dev.yml logs -f
```

**Stop Services:**
```bash
docker-compose -f docker-compose.dev.yml down
```

### 🖥️ Development Server

**Start the API Server:**
```bash
cd chatbot-engine
source ../venv/bin/activate
uvicorn src.online_pipeline.api.main:app --reload --host 0.0.0.0 --port 8000
```

**Or using Make:**
```bash
make start-api
```

### 🧪 Testing

**Run All Tests:**
```bash
make test
```

**Run Specific Test Types:**
```bash
make test-unit          # Unit tests only
make test-integration   # Integration tests only
pytest chatbot-engine/tests/unit/test_api.py -v  # Specific test file
```

### 🔍 Code Quality

**Format Code:**
```bash
make format
```

**Run Linting:**
```bash
make lint
```

**Type Checking:**
```bash
make type-check
```

**Full Quality Check:**
```bash
make dev-check
```

### 📡 API Endpoints

Once the server is running, you can access:

- **API Documentation:** http://localhost:8000/docs
- **Root Endpoint:** http://localhost:8000/
- **Health Check:** http://localhost:8000/health/
- **Chat Query:** POST http://localhost:8000/chat/query
- **Admin Panel:** http://localhost:8000/admin/sources

### 🛠️ Development Tools

**Available Scripts:**
```bash
# Environment setup
python chatbot-engine/scripts/setup_environment.py

# Configuration validation
python chatbot-engine/scripts/validate_config.py

# Development tools
python chatbot-engine/scripts/dev_tools.py --help

# Docker utilities
./chatbot-engine/scripts/docker_utils.sh --help
```

### 📁 Key Directories

- `chatbot-engine/src/` - Source code
- `chatbot-engine/config/` - Configuration files
- `chatbot-engine/data/` - Data storage
- `chatbot-engine/tests/` - Test suites
- `chatbot-engine/logs/` - Application logs

### 🔧 Configuration Files

- `.env` - Environment variables
- `chatbot-engine/config/app_config.yaml` - Main config
- `chatbot-engine/config/development.yaml` - Dev settings
- `chatbot-engine/config/sources.yaml` - Data sources

### 🚨 Troubleshooting

**Common Issues:**

1. **Import Errors:**
   ```bash
   # Make sure you're in the right directory and virtual environment is active
   cd chatbot-engine
   source ../venv/bin/activate
   export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
   ```

2. **Docker Issues:**
   ```bash
   # Reset Docker environment
   docker-compose down
   docker system prune -f
   docker-compose up -d
   ```

3. **Port Conflicts:**
   ```bash
   # Check what's using port 8000
   lsof -i :8000
   # Kill process if needed
   kill -9 <PID>
   ```

### 📊 Service Status Check

**Verify Everything is Working:**
```bash
# 1. Check Python environment
python --version
pip list | grep -E "(fastapi|langchain|pymilvus)"

# 2. Check Docker services
docker-compose ps

# 3. Test API
curl http://localhost:8000/health/

# 4. Run basic test
pytest chatbot-engine/tests/unit/test_api.py::test_root_endpoint -v
```

### 🎯 Next Steps for Phase 2

1. **Implement Offline Pipeline:**
   - Web crawling functionality
   - Document processing
   - Vector embeddings
   - Milvus integration

2. **Set up Data Sources:**
   - Configure German legal websites
   - Add PDF document processing
   - Implement content indexing

3. **Background Processing:**
   - Celery task implementation
   - Automated data updates
   - Monitoring and alerts

### 📞 Support

- Check `PHASE_1_COMPLETION_SUMMARY.md` for detailed setup info
- Review project documentation in `chatbot-engine/documents/`
- Run validation scripts for configuration issues

**Phase 1 is complete! Ready to begin Phase 2 development.** 🎉
