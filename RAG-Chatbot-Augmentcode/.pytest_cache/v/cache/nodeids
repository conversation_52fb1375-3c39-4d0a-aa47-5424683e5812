["chatbot-engine/tests/unit/test_api.py::test_admin_add_source", "chatbot-engine/tests/unit/test_api.py::test_admin_list_sources", "chatbot-engine/tests/unit/test_api.py::test_admin_metrics", "chatbot-engine/tests/unit/test_api.py::test_chat_history", "chatbot-engine/tests/unit/test_api.py::test_chat_query", "chatbot-engine/tests/unit/test_api.py::test_chat_stream", "chatbot-engine/tests/unit/test_api.py::test_clear_chat_session", "chatbot-engine/tests/unit/test_api.py::test_detailed_health_check", "chatbot-engine/tests/unit/test_api.py::test_health_check", "chatbot-engine/tests/unit/test_api.py::test_liveness_check", "chatbot-engine/tests/unit/test_api.py::test_readiness_check", "chatbot-engine/tests/unit/test_api.py::test_root_endpoint"]