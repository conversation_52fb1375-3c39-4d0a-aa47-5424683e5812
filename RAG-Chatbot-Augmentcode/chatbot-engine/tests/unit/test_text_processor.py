"""Unit tests for text processing functionality."""

import pytest
import tempfile
import os
from pathlib import Path

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from offline_pipeline.processing.text_processor import (
    TextExtractor,
    TextChunker,
    LegalTextProcessor
)


@pytest.mark.unit
class TestTextExtractor:
    """Test the TextExtractor class."""
    
    def test_extract_from_html(self):
        """Test HTML text extraction."""
        extractor = TextExtractor()
        
        # Create a temporary HTML file
        html_content = """
        <!DOCTYPE html>
        <html>
        <head><title>Test Document</title></head>
        <body>
            <h1>Legal Document</h1>
            <p>This is a test paragraph about German law.</p>
            <div>§ 1 This is a legal section.</div>
        </body>
        </html>
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(html_content)
            temp_path = f.name
        
        try:
            result = extractor.extract_from_html(temp_path)
            
            assert result["title"] == "Test Document"
            assert result["source_type"] == "html"
            assert "Legal Document" in result["text"]
            assert "German law" in result["text"]
            assert "§ 1" in result["text"]
            assert result["file_path"] == temp_path
            
        finally:
            os.unlink(temp_path)
    
    def test_extract_from_text(self):
        """Test plain text extraction."""
        extractor = TextExtractor()
        
        text_content = """
        This is a test document about German legal principles.
        
        § 1 Basic Rights
        All citizens have fundamental rights under the constitution.
        
        § 2 Legal Procedures
        Legal procedures must follow established protocols.
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(text_content)
            temp_path = f.name
        
        try:
            result = extractor.extract_from_text(temp_path)
            
            assert result["source_type"] == "text"
            assert "German legal principles" in result["text"]
            assert "§ 1 Basic Rights" in result["text"]
            assert result["file_path"] == temp_path
            
        finally:
            os.unlink(temp_path)


@pytest.mark.unit
class TestTextChunker:
    """Test the TextChunker class."""
    
    def test_chunk_document(self):
        """Test document chunking."""
        chunker = TextChunker()
        
        extracted_doc = {
            "text": "This is a long document about German law. " * 100,  # Make it long enough to chunk
            "title": "Test Legal Document",
            "source_type": "text",
            "file_path": "/test/path.txt",
            "metadata": {"author": "Test Author"}
        }
        
        chunks = chunker.chunk_document(extracted_doc, "test_source_id")
        
        assert len(chunks) > 0
        assert all(chunk.source_id == "test_source_id" for chunk in chunks)
        assert all(chunk.metadata["title"] == "Test Legal Document" for chunk in chunks)
        assert all(len(chunk.content) > 0 for chunk in chunks)
        
        # Check that chunk indices are set correctly
        for i, chunk in enumerate(chunks):
            assert chunk.metadata["chunk_index"] == i
    
    def test_chunk_empty_document(self):
        """Test chunking an empty document."""
        chunker = TextChunker()
        
        extracted_doc = {
            "text": "",
            "title": "Empty Document",
            "source_type": "text",
            "file_path": "/test/empty.txt",
            "metadata": {}
        }
        
        chunks = chunker.chunk_document(extracted_doc, "test_source_id")
        assert len(chunks) == 0


@pytest.mark.unit
class TestLegalTextProcessor:
    """Test the LegalTextProcessor class."""
    
    def test_process_html_file(self):
        """Test processing an HTML file."""
        processor = LegalTextProcessor()
        
        html_content = """
        <!DOCTYPE html>
        <html>
        <head><title>BGB Test</title></head>
        <body>
            <h1>Bürgerliches Gesetzbuch</h1>
            <div class="section">
                <h2>§ 1 Beginn der Rechtsfähigkeit</h2>
                <p>Die Rechtsfähigkeit des Menschen beginnt mit der Vollendung der Geburt.</p>
            </div>
            <div class="section">
                <h2>§ 2 Eintritt der Volljährigkeit</h2>
                <p>Die Volljährigkeit tritt mit der Vollendung des 18. Lebensjahres ein.</p>
            </div>
        </body>
        </html>
        """ * 10  # Make it long enough to create multiple chunks
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(html_content)
            temp_path = f.name
        
        try:
            chunks = processor.process_file(temp_path, "bgb_source")
            
            assert len(chunks) > 0
            assert all(chunk.source_id == "bgb_source" for chunk in chunks)
            assert any("Rechtsfähigkeit" in chunk.content for chunk in chunks)
            assert any("Volljährigkeit" in chunk.content for chunk in chunks)
            
        finally:
            os.unlink(temp_path)
    
    def test_process_unsupported_file_type(self):
        """Test processing an unsupported file type."""
        processor = LegalTextProcessor()
        
        with tempfile.NamedTemporaryFile(suffix='.xyz', delete=False) as f:
            temp_path = f.name
        
        try:
            with pytest.raises(ValueError, match="Unsupported file type"):
                processor.process_file(temp_path, "test_source")
        finally:
            os.unlink(temp_path)
    
    def test_process_nonexistent_file(self):
        """Test processing a file that doesn't exist."""
        processor = LegalTextProcessor()
        
        with pytest.raises(FileNotFoundError):
            processor.process_file("/nonexistent/file.txt", "test_source")
    
    def test_process_multiple_files(self):
        """Test processing multiple files."""
        processor = LegalTextProcessor()
        
        # Create multiple temporary files
        file_paths = []
        for i in range(3):
            content = f"This is test document {i} about German law. " * 50
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write(content)
                file_paths.append(f.name)
        
        try:
            chunks = processor.process_multiple_files(file_paths, "multi_source")
            
            assert len(chunks) > 0
            assert all(chunk.source_id == "multi_source" for chunk in chunks)
            
            # Should have chunks from all files
            file_references = set()
            for chunk in chunks:
                file_references.add(chunk.metadata.get("file_path"))
            
            assert len(file_references) == 3
            
        finally:
            for path in file_paths:
                os.unlink(path)
