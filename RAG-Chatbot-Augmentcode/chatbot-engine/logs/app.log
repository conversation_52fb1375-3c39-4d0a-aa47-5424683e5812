2025-07-14 00:09:19 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: GET http://testserver/ "HTTP/1.1 404 Not Found"
2025-07-14 00:09:59 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: GET http://testserver/ "HTTP/1.1 200 OK"
2025-07-14 00:10:05 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: GET http://testserver/ "HTTP/1.1 200 OK"
2025-07-14 00:10:05 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: GET http://testserver/health/ "HTTP/1.1 200 OK"
2025-07-14 00:10:06 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: GET http://testserver/health/detailed "HTTP/1.1 200 OK"
2025-07-14 00:10:06 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: GET http://testserver/health/ready "HTTP/1.1 200 OK"
2025-07-14 00:10:06 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: GET http://testserver/health/live "HTTP/1.1 200 OK"
2025-07-14 00:10:06 - online_pipeline.api.routes.chat - INFO - chat - query_chat - 63 - Processing query: What are the requirements for a valid contract under German law?...
2025-07-14 00:10:06 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: POST http://testserver/chat/query "HTTP/1.1 200 OK"
2025-07-14 00:10:06 - online_pipeline.api.routes.chat - INFO - chat - stream_chat - 98 - Processing streaming query: What are the requirements for a valid contract under German law?...
2025-07-14 00:10:07 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: POST http://testserver/chat/stream "HTTP/1.1 200 OK"
2025-07-14 00:10:07 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: GET http://testserver/chat/sessions/test_session_123/history "HTTP/1.1 200 OK"
2025-07-14 00:10:07 - online_pipeline.api.routes.chat - INFO - chat - clear_chat_session - 149 - Clearing session: test_session_123
2025-07-14 00:10:07 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: DELETE http://testserver/chat/sessions/test_session_123 "HTTP/1.1 200 OK"
2025-07-14 00:10:07 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: GET http://testserver/admin/sources "HTTP/1.1 403 Forbidden"
2025-07-14 00:10:07 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: POST http://testserver/admin/sources "HTTP/1.1 403 Forbidden"
2025-07-14 00:10:07 - httpx - INFO - _client - _send_single_request - 1025 - HTTP Request: GET http://testserver/admin/metrics "HTTP/1.1 403 Forbidden"
