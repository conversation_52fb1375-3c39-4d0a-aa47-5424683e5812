"""Admin endpoints for system management."""

from typing import List, Optional

from fastapi import APIRouter, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(src_path))

from shared.models import (
    AdminSourceRequest,
    DataSource,
    ReindexRequest,
    MetricsResponse
)
from shared.config import get_settings
from shared.logging_config import get_logger

logger = get_logger(__name__)
router = APIRouter()
security = HTTPBearer()


def verify_admin_token(credentials: HTTPAuthorizationCredentials = Security(security)):
    """Verify admin authentication token."""
    settings = get_settings()
    
    if not settings.admin_api_key:
        # If no admin key is configured, allow access (development mode)
        return True
    
    if not credentials or credentials.credentials != settings.admin_api_key:
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication credentials"
        )
    return True


@router.get("/sources", response_model=List[DataSource])
async def list_sources(admin_verified: bool = Depends(verify_admin_token)):
    """List all configured data sources."""
    try:
        # TODO: Implement actual source listing from configuration
        mock_sources = [
            DataSource(
                id="1",
                name="BGB - Bürgerliches Gesetzbuch",
                source_type="website",
                url="https://www.gesetze-im-internet.de/bgb/",
                enabled=True
            ),
            DataSource(
                id="2",
                name="Legal Commentary Volume 1",
                source_type="pdf",
                path="./data/sources/legal_commentary_vol1.pdf",
                enabled=True
            )
        ]
        return mock_sources
    except Exception as e:
        logger.error(f"Error listing sources: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error listing sources")


@router.post("/sources", response_model=DataSource)
async def add_source(
    source_request: AdminSourceRequest,
    admin_verified: bool = Depends(verify_admin_token)
):
    """Add a new data source."""
    try:
        # TODO: Implement actual source addition
        new_source = DataSource(
            id="new_id",
            name=source_request.name,
            source_type=source_request.source_type,
            url=source_request.url,
            path=source_request.path,
            crawl_depth=source_request.crawl_depth,
            enabled=source_request.enabled,
            metadata=source_request.metadata
        )
        
        logger.info(f"Added new source: {new_source.name}")
        return new_source
    except Exception as e:
        logger.error(f"Error adding source: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error adding source")


@router.put("/sources/{source_id}", response_model=DataSource)
async def update_source(
    source_id: str,
    source_request: AdminSourceRequest,
    admin_verified: bool = Depends(verify_admin_token)
):
    """Update an existing data source."""
    try:
        # TODO: Implement actual source update
        updated_source = DataSource(
            id=source_id,
            name=source_request.name,
            source_type=source_request.source_type,
            url=source_request.url,
            path=source_request.path,
            crawl_depth=source_request.crawl_depth,
            enabled=source_request.enabled,
            metadata=source_request.metadata
        )
        
        logger.info(f"Updated source: {source_id}")
        return updated_source
    except Exception as e:
        logger.error(f"Error updating source: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error updating source")


@router.delete("/sources/{source_id}")
async def delete_source(
    source_id: str,
    admin_verified: bool = Depends(verify_admin_token)
):
    """Delete a data source."""
    try:
        # TODO: Implement actual source deletion
        logger.info(f"Deleted source: {source_id}")
        return {"message": f"Source {source_id} deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting source: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error deleting source")


@router.post("/reindex")
async def trigger_reindex(
    reindex_request: ReindexRequest,
    admin_verified: bool = Depends(verify_admin_token)
):
    """Trigger reindexing of data sources."""
    try:
        # TODO: Implement actual reindexing trigger (Celery task)
        logger.info(f"Triggered reindex for sources: {reindex_request.source_ids}")
        
        return {
            "message": "Reindexing started",
            "task_id": "mock_task_id",
            "sources": reindex_request.source_ids or "all"
        }
    except Exception as e:
        logger.error(f"Error triggering reindex: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error triggering reindex")


@router.get("/reindex/status/{task_id}")
async def get_reindex_status(
    task_id: str,
    admin_verified: bool = Depends(verify_admin_token)
):
    """Get the status of a reindexing task."""
    try:
        # TODO: Implement actual task status checking
        return {
            "task_id": task_id,
            "status": "in_progress",
            "progress": 45,
            "message": "Processing documents..."
        }
    except Exception as e:
        logger.error(f"Error getting reindex status: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error getting reindex status")


@router.get("/metrics", response_model=MetricsResponse)
async def get_system_metrics(admin_verified: bool = Depends(verify_admin_token)):
    """Get system metrics and statistics."""
    try:
        # TODO: Implement actual metrics collection
        return MetricsResponse(
            total_documents=1250,
            total_chunks=15600,
            active_sources=3,
            system_health={
                "milvus": "healthy",
                "redis": "healthy",
                "api": "healthy"
            }
        )
    except Exception as e:
        logger.error(f"Error getting metrics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error getting metrics")


@router.post("/system/restart")
async def restart_system(admin_verified: bool = Depends(verify_admin_token)):
    """Restart system components."""
    try:
        # TODO: Implement actual system restart logic
        logger.warning("System restart requested")
        return {"message": "System restart initiated"}
    except Exception as e:
        logger.error(f"Error restarting system: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error restarting system")
