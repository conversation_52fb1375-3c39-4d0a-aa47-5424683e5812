"""Health check endpoints."""

import asyncio
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

import sys
from pathlib import Path

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(src_path))

from shared.config import get_settings
from shared.models import HealthStatus
from shared.logging_config import get_logger

logger = get_logger(__name__)
router = APIRouter()


async def check_milvus_health() -> bool:
    """Check Milvus database connectivity."""
    try:
        # TODO: Implement actual Milvus health check
        # from pymilvus import connections
        # connections.connect(host=settings.milvus_host, port=settings.milvus_port)
        await asyncio.sleep(0.1)  # Simulate check
        return True
    except Exception as e:
        logger.error(f"Milvus health check failed: {e}")
        return False


async def check_redis_health() -> bool:
    """Check Redis connectivity."""
    try:
        # TODO: Implement actual Redis health check
        # import redis
        # r = redis.Redis(host=settings.redis_host, port=settings.redis_port)
        # r.ping()
        await asyncio.sleep(0.1)  # Simulate check
        return True
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        return False


@router.get("/", response_model=HealthStatus)
async def health_check():
    """Basic health check endpoint."""
    settings = get_settings()
    
    return HealthStatus(
        status="healthy",
        version=settings.app_version,
        services={}
    )


@router.get("/detailed", response_model=HealthStatus)
async def detailed_health_check():
    """Detailed health check including external services."""
    settings = get_settings()
    
    # Check external services
    services = {}
    
    try:
        milvus_healthy = await check_milvus_health()
        services["milvus"] = "healthy" if milvus_healthy else "unhealthy"
    except Exception as e:
        services["milvus"] = f"error: {str(e)}"
    
    try:
        redis_healthy = await check_redis_health()
        services["redis"] = "healthy" if redis_healthy else "unhealthy"
    except Exception as e:
        services["redis"] = f"error: {str(e)}"
    
    # Determine overall status
    overall_status = "healthy"
    if any(status != "healthy" for status in services.values()):
        overall_status = "degraded"
    
    return HealthStatus(
        status=overall_status,
        version=settings.app_version,
        services=services
    )


@router.get("/ready")
async def readiness_check():
    """Kubernetes readiness probe endpoint."""
    try:
        # Check if the application is ready to serve requests
        milvus_healthy = await check_milvus_health()
        redis_healthy = await check_redis_health()
        
        if milvus_healthy and redis_healthy:
            return JSONResponse(
                status_code=200,
                content={"status": "ready"}
            )
        else:
            return JSONResponse(
                status_code=503,
                content={"status": "not ready"}
            )
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={"status": "not ready", "error": str(e)}
        )


@router.get("/live")
async def liveness_check():
    """Kubernetes liveness probe endpoint."""
    return JSONResponse(
        status_code=200,
        content={"status": "alive"}
    )
