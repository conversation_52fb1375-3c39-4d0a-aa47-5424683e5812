"""Configuration management for RAG Legal Chatbot."""

import os
from typing import Optional, Dict, Any
from pydantic import Field
from pydantic_settings import BaseSettings
import yaml
from pathlib import Path


def load_config_file(config_path: str) -> Dict[str, Any]:
    """Load configuration from YAML file."""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            return yaml.safe_load(file) or {}
    except FileNotFoundError:
        return {}
    except yaml.YAMLError:
        return {}


def merge_configs(base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
    """Recursively merge configuration dictionaries."""
    result = base_config.copy()
    for key, value in override_config.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_configs(result[key], value)
        else:
            result[key] = value
    return result


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # API Keys
    google_api_key: str = Field("test_google_key", env="GOOGLE_API_KEY")
    cohere_api_key: str = Field("test_cohere_key", env="COHERE_API_KEY")
    
    # Milvus Configuration
    milvus_host: str = Field("localhost", env="MILVUS_HOST")
    milvus_port: int = Field(19530, env="MILVUS_PORT")
    milvus_collection_name: str = Field("legal_docs_v1", env="MILVUS_COLLECTION_NAME")
    
    # Redis Configuration
    redis_host: str = Field("localhost", env="REDIS_HOST")
    redis_port: int = Field(6379, env="REDIS_PORT")
    redis_db: int = Field(0, env="REDIS_DB")
    
    # Application Configuration
    app_name: str = Field("RAG Legal Chatbot", env="APP_NAME")
    app_version: str = Field("1.0.0", env="APP_VERSION")
    debug: bool = Field(False, env="DEBUG")
    log_level: str = Field("INFO", env="LOG_LEVEL")
    
    # API Configuration
    api_host: str = Field("0.0.0.0", env="API_HOST")
    api_port: int = Field(8000, env="API_PORT")
    api_workers: int = Field(1, env="API_WORKERS")
    
    # Embedding Configuration
    embedding_model: str = Field("models/text-embedding-004", env="EMBEDDING_MODEL")
    embedding_dimension: int = Field(768, env="EMBEDDING_DIMENSION")
    
    # LLM Configuration
    llm_model: str = Field("gemini-1.5-flash", env="LLM_MODEL")
    llm_temperature: float = Field(0.1, env="LLM_TEMPERATURE")
    llm_max_tokens: int = Field(2048, env="LLM_MAX_TOKENS")
    
    # Retrieval Configuration
    vector_search_k: int = Field(20, env="VECTOR_SEARCH_K")
    rerank_top_n: int = Field(5, env="RERANK_TOP_N")
    chunk_size: int = Field(1000, env="CHUNK_SIZE")
    chunk_overlap: int = Field(200, env="CHUNK_OVERLAP")
    
    # Data Paths
    data_dir: str = Field("./chatbot-engine/data", env="DATA_DIR")
    sources_config: str = Field("./chatbot-engine/config/sources.yaml", env="SOURCES_CONFIG")
    crawled_data_dir: str = Field("./chatbot-engine/data/crawled", env="CRAWLED_DATA_DIR")
    processed_data_dir: str = Field("./chatbot-engine/data/processed", env="PROCESSED_DATA_DIR")
    indexes_dir: str = Field("./chatbot-engine/data/indexes", env="INDEXES_DIR")
    
    # Security
    secret_key: str = Field("test_secret_key_change_in_production", env="SECRET_KEY")
    admin_api_key: Optional[str] = Field(None, env="ADMIN_API_KEY")
    
    # Monitoring
    enable_metrics: bool = Field(True, env="ENABLE_METRICS")
    metrics_port: int = Field(9090, env="METRICS_PORT")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    def __init__(self, **kwargs):
        """Initialize settings with configuration file loading."""
        # Load base configuration
        config_dir = Path("./chatbot-engine/config")
        base_config = load_config_file(config_dir / "app_config.yaml")

        # Load environment-specific configuration
        environment = os.getenv("ENVIRONMENT", "development")
        env_config = load_config_file(config_dir / f"{environment}.yaml")

        # Merge configurations
        merged_config = merge_configs(base_config, env_config)

        # Apply configuration values to environment variables if not already set
        self._apply_config_to_env(merged_config)

        super().__init__(**kwargs)

    def _apply_config_to_env(self, config: Dict[str, Any], prefix: str = "") -> None:
        """Apply configuration values to environment variables."""
        for key, value in config.items():
            if isinstance(value, dict):
                self._apply_config_to_env(value, f"{prefix}{key.upper()}_")
            else:
                env_key = f"{prefix}{key.upper()}"
                if env_key not in os.environ and value is not None:
                    os.environ[env_key] = str(value)


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get application settings (singleton pattern)."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


def reload_settings() -> Settings:
    """Reload settings (useful for testing)."""
    global _settings
    _settings = Settings()
    return _settings
