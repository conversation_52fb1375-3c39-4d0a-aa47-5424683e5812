"""Celery application configuration for background tasks."""

from celery import Celery
from .config import get_settings

# Get settings
settings = get_settings()

# Create Celery app
celery_app = Celery(
    "rag_chatbot",
    broker=f"redis://{settings.redis_host}:{settings.redis_port}/{settings.redis_db}",
    backend=f"redis://{settings.redis_host}:{settings.redis_port}/{settings.redis_db}",
    include=[
        "chatbot-engine.src.offline_pipeline.tasks",
    ]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    result_expires=3600,  # 1 hour
    worker_hijack_root_logger=False,
    worker_log_format='[%(asctime)s: %(levelname)s/%(processName)s] %(message)s',
    worker_task_log_format='[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s',
)

# Task routes
celery_app.conf.task_routes = {
    'crawl_website_source': {'queue': 'crawling'},
    'process_documents': {'queue': 'processing'},
    'generate_embeddings': {'queue': 'embeddings'},
    'store_vectors': {'queue': 'storage'},
    'process_source_complete': {'queue': 'orchestration'},
    'process_all_sources': {'queue': 'orchestration'},
}

if __name__ == "__main__":
    celery_app.start()
