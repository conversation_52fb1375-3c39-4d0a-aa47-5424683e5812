"""Embedding generation service using Google AI models."""

import asyncio
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod

from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain.schema import Document

from ..shared.models import DocumentChunk
from ..shared.config import get_settings
from ..shared.logging_config import get_logger

logger = get_logger(__name__)


class EmbeddingService(ABC):
    """Abstract base class for embedding services."""
    
    @abstractmethod
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts."""
        pass
    
    @abstractmethod
    async def generate_single_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text."""
        pass


class GoogleAIEmbeddingService(EmbeddingService):
    """Google AI embedding service implementation."""
    
    def __init__(self):
        self.settings = get_settings()
        self.embeddings = GoogleGenerativeAIEmbeddings(
            model=self.settings.embedding_model,
            google_api_key=self.settings.google_api_key
        )
        self.batch_size = 100  # Google AI API batch limit
        
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts."""
        if not texts:
            return []
        
        try:
            # Process in batches to avoid API limits
            all_embeddings = []
            
            for i in range(0, len(texts), self.batch_size):
                batch = texts[i:i + self.batch_size]
                logger.info(f"Processing embedding batch {i//self.batch_size + 1}, size: {len(batch)}")
                
                # Generate embeddings for batch
                batch_embeddings = await asyncio.to_thread(
                    self.embeddings.embed_documents, batch
                )
                all_embeddings.extend(batch_embeddings)
                
                # Small delay to respect rate limits
                if i + self.batch_size < len(texts):
                    await asyncio.sleep(0.1)
            
            logger.info(f"Generated {len(all_embeddings)} embeddings")
            return all_embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            raise
    
    async def generate_single_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text."""
        try:
            embedding = await asyncio.to_thread(
                self.embeddings.embed_query, text
            )
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to generate single embedding: {e}")
            raise
    
    async def embed_chunks(self, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
        """Generate embeddings for document chunks and update them in place."""
        if not chunks:
            return chunks
        
        try:
            # Extract text content from chunks
            texts = [chunk.content for chunk in chunks]
            
            # Generate embeddings
            embeddings = await self.generate_embeddings(texts)
            
            # Update chunks with embeddings
            for chunk, embedding in zip(chunks, embeddings):
                chunk.embedding = embedding
            
            logger.info(f"Embedded {len(chunks)} document chunks")
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to embed chunks: {e}")
            raise


class EmbeddingProcessor:
    """High-level processor for handling document embedding workflows."""
    
    def __init__(self, embedding_service: Optional[EmbeddingService] = None):
        self.embedding_service = embedding_service or GoogleAIEmbeddingService()
        self.settings = get_settings()
    
    async def process_chunks_batch(self, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
        """Process a batch of chunks to generate embeddings."""
        if not chunks:
            return []
        
        try:
            logger.info(f"Processing {len(chunks)} chunks for embedding generation")
            
            # Filter out chunks that already have embeddings
            chunks_to_embed = [chunk for chunk in chunks if not chunk.embedding]
            
            if not chunks_to_embed:
                logger.info("All chunks already have embeddings")
                return chunks
            
            logger.info(f"Generating embeddings for {len(chunks_to_embed)} new chunks")
            
            # Generate embeddings
            embedded_chunks = await self.embedding_service.embed_chunks(chunks_to_embed)
            
            # Combine with already embedded chunks
            result_chunks = []
            embedded_dict = {chunk.id: chunk for chunk in embedded_chunks}
            
            for chunk in chunks:
                if chunk.id in embedded_dict:
                    result_chunks.append(embedded_dict[chunk.id])
                else:
                    result_chunks.append(chunk)
            
            logger.info(f"Successfully processed {len(result_chunks)} chunks")
            return result_chunks
            
        except Exception as e:
            logger.error(f"Failed to process chunks batch: {e}")
            raise
    
    async def process_chunks_stream(self, chunks: List[DocumentChunk], 
                                  batch_size: int = 50) -> List[DocumentChunk]:
        """Process chunks in smaller batches for memory efficiency."""
        if not chunks:
            return []
        
        try:
            all_processed = []
            
            for i in range(0, len(chunks), batch_size):
                batch = chunks[i:i + batch_size]
                logger.info(f"Processing chunk batch {i//batch_size + 1}/{(len(chunks) + batch_size - 1)//batch_size}")
                
                processed_batch = await self.process_chunks_batch(batch)
                all_processed.extend(processed_batch)
                
                # Small delay between batches
                if i + batch_size < len(chunks):
                    await asyncio.sleep(0.5)
            
            logger.info(f"Completed streaming processing of {len(all_processed)} chunks")
            return all_processed
            
        except Exception as e:
            logger.error(f"Failed to process chunks stream: {e}")
            raise
    
    async def validate_embeddings(self, chunks: List[DocumentChunk]) -> Dict[str, Any]:
        """Validate that chunks have proper embeddings."""
        total_chunks = len(chunks)
        embedded_chunks = len([chunk for chunk in chunks if chunk.embedding])
        missing_embeddings = total_chunks - embedded_chunks
        
        # Check embedding dimensions
        dimension_counts = {}
        for chunk in chunks:
            if chunk.embedding:
                dim = len(chunk.embedding)
                dimension_counts[dim] = dimension_counts.get(dim, 0) + 1
        
        expected_dimension = self.settings.embedding_dimension
        correct_dimension = dimension_counts.get(expected_dimension, 0)
        
        validation_result = {
            "total_chunks": total_chunks,
            "embedded_chunks": embedded_chunks,
            "missing_embeddings": missing_embeddings,
            "embedding_coverage": embedded_chunks / total_chunks if total_chunks > 0 else 0,
            "expected_dimension": expected_dimension,
            "correct_dimension_count": correct_dimension,
            "dimension_distribution": dimension_counts,
            "validation_passed": missing_embeddings == 0 and correct_dimension == embedded_chunks
        }
        
        logger.info(f"Embedding validation: {validation_result}")
        return validation_result
    
    async def reprocess_failed_embeddings(self, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
        """Reprocess chunks that failed embedding generation."""
        failed_chunks = []
        
        for chunk in chunks:
            if not chunk.embedding:
                failed_chunks.append(chunk)
            elif len(chunk.embedding) != self.settings.embedding_dimension:
                logger.warning(f"Chunk {chunk.id} has incorrect embedding dimension: {len(chunk.embedding)}")
                chunk.embedding = None  # Reset to reprocess
                failed_chunks.append(chunk)
        
        if failed_chunks:
            logger.info(f"Reprocessing {len(failed_chunks)} failed embeddings")
            return await self.process_chunks_batch(failed_chunks)
        
        return chunks


# Factory function for easy instantiation
def create_embedding_processor() -> EmbeddingProcessor:
    """Create an embedding processor with default Google AI service."""
    return EmbeddingProcessor(GoogleAIEmbeddingService())
