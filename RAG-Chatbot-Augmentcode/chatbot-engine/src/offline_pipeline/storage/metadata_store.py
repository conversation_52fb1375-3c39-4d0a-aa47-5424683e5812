"""Metadata storage for document processing tracking."""

import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

from ...shared.models import DataSource, ProcessingStatus
from ...shared.config import get_settings
from ...shared.logging_config import get_logger

logger = get_logger(__name__)


class MetadataStore:
    """SQLite-based metadata storage for tracking document processing."""
    
    def __init__(self):
        self.settings = get_settings()
        self.db_path = Path(self.settings.data_dir) / "metadata.db"
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
    async def initialize(self) -> None:
        """Initialize the metadata database."""
        try:
            async with self._get_connection() as conn:
                await self._create_tables(conn)
            logger.info(f"Initialized metadata store at {self.db_path}")
        except Exception as e:
            logger.error(f"Failed to initialize metadata store: {e}")
            raise
    
    @asynccontextmanager
    async def _get_connection(self):
        """Get database connection with proper error handling."""
        conn = None
        try:
            conn = sqlite3.connect(str(self.db_path))
            conn.row_factory = sqlite3.Row
            yield conn
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    async def _create_tables(self, conn: sqlite3.Connection) -> None:
        """Create necessary database tables."""
        cursor = conn.cursor()
        
        # Data sources table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS data_sources (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                source_type TEXT NOT NULL,
                url TEXT,
                path TEXT,
                crawl_depth INTEGER,
                enabled BOOLEAN DEFAULT 1,
                last_processed TIMESTAMP,
                status TEXT DEFAULT 'pending',
                metadata TEXT DEFAULT '{}',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Document chunks tracking table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS document_chunks (
                id TEXT PRIMARY KEY,
                source_id TEXT NOT NULL,
                source_url TEXT,
                content_hash TEXT,
                chunk_count INTEGER DEFAULT 0,
                processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT DEFAULT '{}',
                FOREIGN KEY (source_id) REFERENCES data_sources (id)
            )
        """)
        
        # Processing logs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS processing_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_id TEXT NOT NULL,
                operation TEXT NOT NULL,
                status TEXT NOT NULL,
                message TEXT,
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP,
                metadata TEXT DEFAULT '{}',
                FOREIGN KEY (source_id) REFERENCES data_sources (id)
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sources_status ON data_sources(status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_chunks_source ON document_chunks(source_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_logs_source ON processing_logs(source_id)")
        
        conn.commit()
    
    async def store_data_source(self, source: DataSource) -> bool:
        """Store or update a data source."""
        try:
            async with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO data_sources 
                    (id, name, source_type, url, path, crawl_depth, enabled, 
                     last_processed, status, metadata, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    source.id or source.name,
                    source.name,
                    source.source_type.value,
                    source.url,
                    source.path,
                    source.crawl_depth,
                    source.enabled,
                    source.last_processed,
                    source.status.value,
                    json.dumps(source.metadata),
                    datetime.utcnow()
                ))
                
                conn.commit()
                logger.info(f"Stored data source: {source.name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to store data source {source.name}: {e}")
            return False
    
    async def get_data_source(self, source_id: str) -> Optional[DataSource]:
        """Get a data source by ID."""
        try:
            async with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM data_sources WHERE id = ?", (source_id,))
                row = cursor.fetchone()
                
                if row:
                    return DataSource(
                        id=row["id"],
                        name=row["name"],
                        source_type=row["source_type"],
                        url=row["url"],
                        path=row["path"],
                        crawl_depth=row["crawl_depth"],
                        enabled=bool(row["enabled"]),
                        last_processed=datetime.fromisoformat(row["last_processed"]) if row["last_processed"] else None,
                        status=ProcessingStatus(row["status"]),
                        metadata=json.loads(row["metadata"])
                    )
                return None
                
        except Exception as e:
            logger.error(f"Failed to get data source {source_id}: {e}")
            return None
    
    async def get_all_data_sources(self, enabled_only: bool = False) -> List[DataSource]:
        """Get all data sources."""
        try:
            async with self._get_connection() as conn:
                cursor = conn.cursor()
                
                query = "SELECT * FROM data_sources"
                if enabled_only:
                    query += " WHERE enabled = 1"
                query += " ORDER BY name"
                
                cursor.execute(query)
                rows = cursor.fetchall()
                
                sources = []
                for row in rows:
                    source = DataSource(
                        id=row["id"],
                        name=row["name"],
                        source_type=row["source_type"],
                        url=row["url"],
                        path=row["path"],
                        crawl_depth=row["crawl_depth"],
                        enabled=bool(row["enabled"]),
                        last_processed=datetime.fromisoformat(row["last_processed"]) if row["last_processed"] else None,
                        status=ProcessingStatus(row["status"]),
                        metadata=json.loads(row["metadata"])
                    )
                    sources.append(source)
                
                return sources
                
        except Exception as e:
            logger.error(f"Failed to get data sources: {e}")
            return []
    
    async def update_source_status(self, source_id: str, status: ProcessingStatus, 
                                 last_processed: Optional[datetime] = None) -> bool:
        """Update the processing status of a data source."""
        try:
            async with self._get_connection() as conn:
                cursor = conn.cursor()
                
                if last_processed is None:
                    last_processed = datetime.utcnow()
                
                cursor.execute("""
                    UPDATE data_sources 
                    SET status = ?, last_processed = ?, updated_at = ?
                    WHERE id = ?
                """, (status.value, last_processed, datetime.utcnow(), source_id))
                
                conn.commit()
                logger.info(f"Updated source {source_id} status to {status.value}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update source status for {source_id}: {e}")
            return False
    
    async def log_processing_operation(self, source_id: str, operation: str, 
                                     status: str, message: str = "", 
                                     metadata: Dict[str, Any] = None) -> bool:
        """Log a processing operation."""
        try:
            async with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO processing_logs 
                    (source_id, operation, status, message, metadata)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    source_id,
                    operation,
                    status,
                    message,
                    json.dumps(metadata or {})
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to log processing operation: {e}")
            return False
    
    async def track_document_chunks(self, source_id: str, source_url: str, 
                                  content_hash: str, chunk_count: int) -> bool:
        """Track processed document chunks."""
        try:
            async with self._get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO document_chunks 
                    (id, source_id, source_url, content_hash, chunk_count, processed_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    f"{source_id}_{content_hash[:8]}",
                    source_id,
                    source_url,
                    content_hash,
                    chunk_count,
                    datetime.utcnow()
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to track document chunks: {e}")
            return False
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        try:
            async with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # Count sources by status
                cursor.execute("""
                    SELECT status, COUNT(*) as count 
                    FROM data_sources 
                    GROUP BY status
                """)
                status_counts = dict(cursor.fetchall())
                
                # Total chunks
                cursor.execute("SELECT SUM(chunk_count) as total FROM document_chunks")
                total_chunks = cursor.fetchone()["total"] or 0
                
                # Recent processing activity
                cursor.execute("""
                    SELECT COUNT(*) as count 
                    FROM processing_logs 
                    WHERE started_at > datetime('now', '-24 hours')
                """)
                recent_activity = cursor.fetchone()["count"]
                
                return {
                    "total_sources": sum(status_counts.values()),
                    "status_breakdown": status_counts,
                    "total_chunks": total_chunks,
                    "recent_activity_24h": recent_activity,
                    "last_updated": datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Failed to get processing stats: {e}")
            return {}
