"""Vector storage implementation using Milvus."""

import json
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from pymilvus import (
    connections, 
    Collection, 
    CollectionSchema, 
    FieldSchema, 
    DataType,
    utility
)

from ...shared.models import DocumentChunk
from ...shared.config import get_settings
from ...shared.logging_config import get_logger

logger = get_logger(__name__)


class VectorStore(ABC):
    """Abstract base class for vector storage."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the vector store."""
        pass
    
    @abstractmethod
    async def store_chunks(self, chunks: List[DocumentChunk]) -> bool:
        """Store document chunks with their embeddings."""
        pass
    
    @abstractmethod
    async def search_similar(self, query_embedding: List[float], top_k: int = 10) -> List[Dict[str, Any]]:
        """Search for similar vectors."""
        pass
    
    @abstractmethod
    async def delete_by_source(self, source_id: str) -> bool:
        """Delete all chunks from a specific source."""
        pass
    
    @abstractmethod
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics."""
        pass


class MilvusVectorStore(VectorStore):
    """Milvus implementation of vector storage."""
    
    def __init__(self):
        self.settings = get_settings()
        self.collection_name = self.settings.milvus_collection_name
        self.collection: Optional[Collection] = None
        self.dimension = 768  # Default for Google AI embeddings
        
    async def initialize(self) -> None:
        """Initialize Milvus connection and collection."""
        try:
            # Connect to Milvus
            connections.connect(
                alias="default",
                host=self.settings.milvus_host,
                port=self.settings.milvus_port
            )
            logger.info(f"Connected to Milvus at {self.settings.milvus_host}:{self.settings.milvus_port}")
            
            # Create collection if it doesn't exist
            if not utility.has_collection(self.collection_name):
                await self._create_collection()
            
            # Load collection
            self.collection = Collection(self.collection_name)
            self.collection.load()
            logger.info(f"Loaded collection: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Milvus: {e}")
            raise
    
    async def _create_collection(self) -> None:
        """Create the Milvus collection with proper schema."""
        try:
            # Define collection schema
            fields = [
                FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=100, is_primary=True),
                FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="source_id", dtype=DataType.VARCHAR, max_length=100),
                FieldSchema(name="source_url", dtype=DataType.VARCHAR, max_length=500),
                FieldSchema(name="metadata", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=self.dimension),
                FieldSchema(name="created_at", dtype=DataType.VARCHAR, max_length=50)
            ]
            
            schema = CollectionSchema(
                fields=fields,
                description="Legal document chunks with embeddings"
            )
            
            # Create collection
            collection = Collection(
                name=self.collection_name,
                schema=schema,
                using='default'
            )
            
            # Create index for vector field
            index_params = {
                "metric_type": "COSINE",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 1024}
            }
            collection.create_index(field_name="embedding", index_params=index_params)
            
            logger.info(f"Created collection: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"Failed to create collection: {e}")
            raise
    
    async def store_chunks(self, chunks: List[DocumentChunk]) -> bool:
        """Store document chunks with their embeddings."""
        if not chunks:
            return True
            
        if not self.collection:
            raise RuntimeError("Collection not initialized")
        
        try:
            # Prepare data for insertion
            data = []
            for chunk in chunks:
                if not chunk.embedding:
                    logger.warning(f"Chunk {chunk.id} has no embedding, skipping")
                    continue
                
                data.append({
                    "id": chunk.id,
                    "content": chunk.content[:65535],  # Truncate if too long
                    "source_id": chunk.source_id,
                    "source_url": chunk.source_url or "",
                    "metadata": json.dumps(chunk.metadata),
                    "embedding": chunk.embedding,
                    "created_at": chunk.created_at.isoformat()
                })
            
            if not data:
                logger.warning("No valid chunks to store")
                return True
            
            # Insert data
            self.collection.insert(data)
            self.collection.flush()
            
            logger.info(f"Stored {len(data)} chunks in Milvus")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store chunks: {e}")
            return False
    
    async def search_similar(self, query_embedding: List[float], top_k: int = 10) -> List[Dict[str, Any]]:
        """Search for similar vectors."""
        if not self.collection:
            raise RuntimeError("Collection not initialized")
        
        try:
            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }
            
            results = self.collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                output_fields=["id", "content", "source_id", "source_url", "metadata", "created_at"]
            )
            
            # Format results
            formatted_results = []
            for hit in results[0]:
                result = {
                    "id": hit.entity.get("id"),
                    "content": hit.entity.get("content"),
                    "source_id": hit.entity.get("source_id"),
                    "source_url": hit.entity.get("source_url"),
                    "metadata": json.loads(hit.entity.get("metadata", "{}")),
                    "created_at": hit.entity.get("created_at"),
                    "score": hit.score
                }
                formatted_results.append(result)
            
            logger.info(f"Found {len(formatted_results)} similar chunks")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to search vectors: {e}")
            return []
    
    async def delete_by_source(self, source_id: str) -> bool:
        """Delete all chunks from a specific source."""
        if not self.collection:
            raise RuntimeError("Collection not initialized")
        
        try:
            expr = f'source_id == "{source_id}"'
            self.collection.delete(expr)
            self.collection.flush()
            
            logger.info(f"Deleted chunks for source: {source_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete chunks for source {source_id}: {e}")
            return False
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics."""
        if not self.collection:
            raise RuntimeError("Collection not initialized")
        
        try:
            stats = self.collection.get_stats()
            return {
                "total_entities": stats.get("row_count", 0),
                "collection_name": self.collection_name,
                "dimension": self.dimension,
                "index_type": "IVF_FLAT",
                "metric_type": "COSINE"
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {}
    
    async def close(self) -> None:
        """Close the connection."""
        try:
            if self.collection:
                self.collection.release()
            connections.disconnect("default")
            logger.info("Disconnected from Milvus")
        except Exception as e:
            logger.error(f"Error closing Milvus connection: {e}")
