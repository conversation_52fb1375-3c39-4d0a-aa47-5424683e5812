"""Data source management system for offline pipeline."""

import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..shared.models import DataSource, SourceType, ProcessingStatus
from ..shared.config import get_settings
from ..shared.logging_config import get_logger
from ..shared.utils import load_yaml_file, save_yaml_file, generate_id

from .storage.metadata_store import MetadataStore

logger = get_logger(__name__)


class DataSourceManager:
    """Manager for data source configuration and lifecycle."""
    
    def __init__(self):
        self.settings = get_settings()
        self.config_path = Path(self.settings.sources_config)
        self.metadata_store: Optional[MetadataStore] = None
    
    async def initialize(self) -> None:
        """Initialize the data source manager."""
        try:
            self.metadata_store = MetadataStore()
            await self.metadata_store.initialize()
            logger.info("Data source manager initialized")
        except Exception as e:
            logger.error(f"Failed to initialize data source manager: {e}")
            raise
    
    async def load_sources_from_config(self) -> List[DataSource]:
        """Load data sources from configuration file."""
        try:
            if not self.config_path.exists():
                logger.warning(f"Sources config file not found: {self.config_path}")
                return []
            
            config = load_yaml_file(str(self.config_path))
            if not config:
                logger.warning("Empty or invalid sources configuration")
                return []
            
            sources = []
            
            # Load website sources
            for website_config in config.get('websites', []):
                source = DataSource(
                    id=website_config.get('id') or generate_id(),
                    name=website_config['name'],
                    source_type=SourceType.WEBSITE,
                    url=website_config['url'],
                    crawl_depth=website_config.get('crawl_depth', 2),
                    enabled=website_config.get('enabled', True),
                    metadata=website_config.get('metadata', {})
                )
                sources.append(source)
            
            # Load PDF sources
            for pdf_config in config.get('local_pdfs', []):
                source = DataSource(
                    id=pdf_config.get('id') or generate_id(),
                    name=pdf_config['name'],
                    source_type=SourceType.PDF,
                    path=pdf_config['path'],
                    enabled=pdf_config.get('enabled', True),
                    metadata=pdf_config.get('metadata', {})
                )
                sources.append(source)
            
            logger.info(f"Loaded {len(sources)} data sources from configuration")
            return sources
            
        except Exception as e:
            logger.error(f"Failed to load sources from config: {e}")
            return []
    
    async def sync_sources_to_database(self) -> bool:
        """Sync configuration sources to metadata database."""
        if not self.metadata_store:
            raise RuntimeError("Metadata store not initialized")
        
        try:
            config_sources = await self.load_sources_from_config()
            
            for source in config_sources:
                # Check if source exists in database
                existing_source = await self.metadata_store.get_data_source(source.id)
                
                if existing_source:
                    # Update existing source if configuration changed
                    if self._source_changed(existing_source, source):
                        logger.info(f"Updating source: {source.name}")
                        await self.metadata_store.store_data_source(source)
                else:
                    # Add new source
                    logger.info(f"Adding new source: {source.name}")
                    await self.metadata_store.store_data_source(source)
            
            logger.info(f"Synced {len(config_sources)} sources to database")
            return True
            
        except Exception as e:
            logger.error(f"Failed to sync sources to database: {e}")
            return False
    
    def _source_changed(self, existing: DataSource, new: DataSource) -> bool:
        """Check if a source configuration has changed."""
        # Compare key fields that would require reprocessing
        return (
            existing.url != new.url or
            existing.path != new.path or
            existing.crawl_depth != new.crawl_depth or
            existing.enabled != new.enabled
        )
    
    async def get_all_sources(self, enabled_only: bool = False) -> List[DataSource]:
        """Get all data sources from database."""
        if not self.metadata_store:
            raise RuntimeError("Metadata store not initialized")
        
        return await self.metadata_store.get_all_data_sources(enabled_only)
    
    async def get_source(self, source_id: str) -> Optional[DataSource]:
        """Get a specific data source by ID."""
        if not self.metadata_store:
            raise RuntimeError("Metadata store not initialized")
        
        return await self.metadata_store.get_data_source(source_id)
    
    async def add_source(self, source: DataSource) -> bool:
        """Add a new data source."""
        if not self.metadata_store:
            raise RuntimeError("Metadata store not initialized")
        
        try:
            # Generate ID if not provided
            if not source.id:
                source.id = generate_id()
            
            # Store in database
            success = await self.metadata_store.store_data_source(source)
            
            if success:
                logger.info(f"Added new source: {source.name}")
                # Optionally update configuration file
                await self._update_config_file()
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to add source {source.name}: {e}")
            return False
    
    async def update_source(self, source: DataSource) -> bool:
        """Update an existing data source."""
        if not self.metadata_store:
            raise RuntimeError("Metadata store not initialized")
        
        try:
            success = await self.metadata_store.store_data_source(source)
            
            if success:
                logger.info(f"Updated source: {source.name}")
                # Optionally update configuration file
                await self._update_config_file()
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to update source {source.name}: {e}")
            return False
    
    async def disable_source(self, source_id: str) -> bool:
        """Disable a data source."""
        try:
            source = await self.get_source(source_id)
            if not source:
                logger.warning(f"Source not found: {source_id}")
                return False
            
            source.enabled = False
            return await self.update_source(source)
            
        except Exception as e:
            logger.error(f"Failed to disable source {source_id}: {e}")
            return False
    
    async def enable_source(self, source_id: str) -> bool:
        """Enable a data source."""
        try:
            source = await self.get_source(source_id)
            if not source:
                logger.warning(f"Source not found: {source_id}")
                return False
            
            source.enabled = True
            return await self.update_source(source)
            
        except Exception as e:
            logger.error(f"Failed to enable source {source_id}: {e}")
            return False
    
    async def get_sources_by_status(self, status: ProcessingStatus) -> List[DataSource]:
        """Get sources filtered by processing status."""
        all_sources = await self.get_all_sources()
        return [source for source in all_sources if source.status == status]
    
    async def get_sources_needing_update(self, hours: int = 24) -> List[DataSource]:
        """Get sources that haven't been processed recently."""
        all_sources = await self.get_all_sources(enabled_only=True)
        cutoff_time = datetime.utcnow().timestamp() - (hours * 3600)
        
        needing_update = []
        for source in all_sources:
            if not source.last_processed:
                needing_update.append(source)
            elif source.last_processed.timestamp() < cutoff_time:
                needing_update.append(source)
        
        return needing_update
    
    async def validate_sources(self) -> Dict[str, Any]:
        """Validate all data sources and return validation report."""
        try:
            sources = await self.get_all_sources()
            validation_results = {
                "total_sources": len(sources),
                "enabled_sources": 0,
                "valid_sources": 0,
                "invalid_sources": [],
                "warnings": []
            }
            
            for source in sources:
                if source.enabled:
                    validation_results["enabled_sources"] += 1
                
                # Validate source configuration
                is_valid, issues = await self._validate_single_source(source)
                
                if is_valid:
                    validation_results["valid_sources"] += 1
                else:
                    validation_results["invalid_sources"].append({
                        "source_id": source.id,
                        "source_name": source.name,
                        "issues": issues
                    })
            
            logger.info(f"Source validation completed: {validation_results}")
            return validation_results
            
        except Exception as e:
            logger.error(f"Source validation failed: {e}")
            return {"error": str(e)}
    
    async def _validate_single_source(self, source: DataSource) -> tuple[bool, List[str]]:
        """Validate a single data source."""
        issues = []
        
        # Check required fields
        if not source.name:
            issues.append("Source name is required")
        
        # Validate based on source type
        if source.source_type == SourceType.WEBSITE:
            if not source.url:
                issues.append("Website source must have a URL")
            elif not source.url.startswith(('http://', 'https://')):
                issues.append("Website URL must start with http:// or https://")
        
        elif source.source_type == SourceType.PDF:
            if not source.path:
                issues.append("PDF source must have a file path")
            elif not Path(source.path).exists():
                issues.append(f"PDF file not found: {source.path}")
        
        # Check crawl depth
        if source.crawl_depth and (source.crawl_depth < 1 or source.crawl_depth > 10):
            issues.append("Crawl depth must be between 1 and 10")
        
        return len(issues) == 0, issues
    
    async def _update_config_file(self) -> None:
        """Update the configuration file with current database state."""
        try:
            sources = await self.get_all_sources()
            
            # Group sources by type
            websites = []
            local_pdfs = []
            
            for source in sources:
                if source.source_type == SourceType.WEBSITE:
                    websites.append({
                        "id": source.id,
                        "name": source.name,
                        "url": source.url,
                        "crawl_depth": source.crawl_depth,
                        "enabled": source.enabled,
                        "metadata": source.metadata
                    })
                elif source.source_type == SourceType.PDF:
                    local_pdfs.append({
                        "id": source.id,
                        "name": source.name,
                        "path": source.path,
                        "enabled": source.enabled,
                        "metadata": source.metadata
                    })
            
            # Load existing config to preserve other settings
            existing_config = load_yaml_file(str(self.config_path)) or {}
            
            # Update source sections
            existing_config["websites"] = websites
            existing_config["local_pdfs"] = local_pdfs
            
            # Save updated configuration
            save_yaml_file(str(self.config_path), existing_config)
            logger.info("Updated configuration file")
            
        except Exception as e:
            logger.error(f"Failed to update config file: {e}")


# Factory function for easy instantiation
async def create_source_manager() -> DataSourceManager:
    """Create and initialize a data source manager."""
    manager = DataSourceManager()
    await manager.initialize()
    return manager
