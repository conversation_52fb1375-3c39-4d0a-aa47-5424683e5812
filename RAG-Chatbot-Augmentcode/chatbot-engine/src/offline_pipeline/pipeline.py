"""Main offline pipeline orchestrator."""

import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable
from enum import Enum

from ..shared.models import DataSource, ProcessingStatus
from ..shared.config import get_settings
from ..shared.logging_config import get_logger

from .source_manager import DataSourceManager
from .storage.metadata_store import MetadataStore
from .tasks import (
    process_source_complete,
    process_all_sources,
    crawl_website_source,
    process_documents,
    generate_embeddings,
    store_vectors
)

logger = get_logger(__name__)


class PipelineMode(str, Enum):
    """Pipeline execution modes."""
    FULL = "full"  # Complete processing including crawling
    INCREMENTAL = "incremental"  # Only process new/changed sources
    REPROCESS = "reprocess"  # Reprocess existing sources
    VALIDATE = "validate"  # Validate pipeline without processing


class OfflinePipeline:
    """Main orchestrator for the offline data processing pipeline."""
    
    def __init__(self):
        self.settings = get_settings()
        self.source_manager: Optional[DataSourceManager] = None
        self.metadata_store: Optional[MetadataStore] = None
        self.is_running = False
        self.current_task = None
        
        # Pipeline statistics
        self.stats = {
            "start_time": None,
            "end_time": None,
            "total_sources": 0,
            "processed_sources": 0,
            "failed_sources": 0,
            "total_chunks": 0,
            "errors": []
        }
    
    async def initialize(self) -> None:
        """Initialize the pipeline components."""
        try:
            logger.info("Initializing offline pipeline")
            
            # Initialize source manager
            self.source_manager = DataSourceManager()
            await self.source_manager.initialize()
            
            # Initialize metadata store
            self.metadata_store = MetadataStore()
            await self.metadata_store.initialize()
            
            # Sync sources from config to database
            await self.source_manager.sync_sources_to_database()
            
            logger.info("Offline pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize pipeline: {e}")
            raise
    
    async def run_pipeline(self, mode: PipelineMode = PipelineMode.FULL, 
                          source_ids: Optional[List[str]] = None,
                          progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Run the offline processing pipeline.
        
        Args:
            mode: Pipeline execution mode
            source_ids: Optional list of specific source IDs to process
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dictionary with pipeline execution results
        """
        if self.is_running:
            raise RuntimeError("Pipeline is already running")
        
        try:
            self.is_running = True
            self.stats["start_time"] = datetime.utcnow()
            
            logger.info(f"Starting pipeline in {mode.value} mode")
            
            if progress_callback:
                await progress_callback("Initializing pipeline", 0)
            
            # Get sources to process
            sources = await self._get_sources_for_mode(mode, source_ids)
            self.stats["total_sources"] = len(sources)
            
            if not sources:
                logger.warning("No sources found to process")
                return self._get_final_stats()
            
            logger.info(f"Processing {len(sources)} sources")
            
            # Process sources based on mode
            if mode == PipelineMode.VALIDATE:
                return await self._validate_pipeline(sources, progress_callback)
            else:
                return await self._process_sources(sources, mode, progress_callback)
                
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            self.stats["errors"].append(str(e))
            raise
        finally:
            self.is_running = False
            self.stats["end_time"] = datetime.utcnow()
    
    async def _get_sources_for_mode(self, mode: PipelineMode, 
                                  source_ids: Optional[List[str]]) -> List[DataSource]:
        """Get sources to process based on mode and filters."""
        if source_ids:
            # Process specific sources
            sources = []
            for source_id in source_ids:
                source = await self.source_manager.get_source(source_id)
                if source and source.enabled:
                    sources.append(source)
            return sources
        
        if mode == PipelineMode.FULL:
            # Process all enabled sources
            return await self.source_manager.get_all_sources(enabled_only=True)
        
        elif mode == PipelineMode.INCREMENTAL:
            # Process sources that need updates
            return await self.source_manager.get_sources_needing_update(hours=24)
        
        elif mode == PipelineMode.REPROCESS:
            # Process sources that failed or completed sources for reprocessing
            failed_sources = await self.source_manager.get_sources_by_status(ProcessingStatus.FAILED)
            completed_sources = await self.source_manager.get_sources_by_status(ProcessingStatus.COMPLETED)
            return failed_sources + completed_sources
        
        elif mode == PipelineMode.VALIDATE:
            # Validate all sources
            return await self.source_manager.get_all_sources()
        
        return []
    
    async def _process_sources(self, sources: List[DataSource], mode: PipelineMode,
                             progress_callback: Optional[Callable]) -> Dict[str, Any]:
        """Process the list of sources."""
        results = {}
        
        for i, source in enumerate(sources):
            try:
                if progress_callback:
                    progress = int((i / len(sources)) * 100)
                    await progress_callback(f"Processing {source.name}", progress)
                
                logger.info(f"Processing source {i+1}/{len(sources)}: {source.name}")
                
                # Submit processing task
                task_result = process_source_complete.apply_async(args=[source.dict()])
                
                # Wait for completion with timeout
                try:
                    result = task_result.get(timeout=1800)  # 30 minutes timeout
                    
                    if result.get('success'):
                        self.stats["processed_sources"] += 1
                        self.stats["total_chunks"] += result.get('chunk_count', 0)
                        logger.info(f"Successfully processed {source.name}")
                    else:
                        self.stats["failed_sources"] += 1
                        logger.error(f"Failed to process {source.name}")
                    
                    results[source.name] = result
                    
                except Exception as e:
                    self.stats["failed_sources"] += 1
                    self.stats["errors"].append(f"{source.name}: {str(e)}")
                    results[source.name] = {"success": False, "error": str(e)}
                    logger.error(f"Processing failed for {source.name}: {e}")
                
            except Exception as e:
                self.stats["failed_sources"] += 1
                self.stats["errors"].append(f"{source.name}: {str(e)}")
                results[source.name] = {"success": False, "error": str(e)}
                logger.error(f"Error processing {source.name}: {e}")
        
        if progress_callback:
            await progress_callback("Pipeline completed", 100)
        
        final_stats = self._get_final_stats()
        final_stats["source_results"] = results
        
        return final_stats
    
    async def _validate_pipeline(self, sources: List[DataSource],
                               progress_callback: Optional[Callable]) -> Dict[str, Any]:
        """Validate pipeline configuration and sources."""
        logger.info("Running pipeline validation")
        
        validation_results = {
            "pipeline_valid": True,
            "source_validation": {},
            "configuration_issues": [],
            "recommendations": []
        }
        
        try:
            # Validate sources
            if progress_callback:
                await progress_callback("Validating sources", 25)
            
            source_validation = await self.source_manager.validate_sources()
            validation_results["source_validation"] = source_validation
            
            # Check configuration
            if progress_callback:
                await progress_callback("Checking configuration", 50)
            
            config_issues = await self._validate_configuration()
            validation_results["configuration_issues"] = config_issues
            
            # Check dependencies
            if progress_callback:
                await progress_callback("Checking dependencies", 75)
            
            dependency_issues = await self._validate_dependencies()
            validation_results["dependency_issues"] = dependency_issues
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                source_validation, config_issues, dependency_issues
            )
            validation_results["recommendations"] = recommendations
            
            # Determine overall validity
            validation_results["pipeline_valid"] = (
                len(config_issues) == 0 and
                len(dependency_issues) == 0 and
                len(source_validation.get("invalid_sources", [])) == 0
            )
            
            if progress_callback:
                await progress_callback("Validation completed", 100)
            
            logger.info(f"Pipeline validation completed: {validation_results}")
            return validation_results
            
        except Exception as e:
            logger.error(f"Pipeline validation failed: {e}")
            validation_results["pipeline_valid"] = False
            validation_results["validation_error"] = str(e)
            return validation_results
    
    async def _validate_configuration(self) -> List[str]:
        """Validate pipeline configuration."""
        issues = []
        
        # Check required API keys
        if not self.settings.google_api_key or self.settings.google_api_key == "test_google_key":
            issues.append("Google API key not configured")
        
        if not self.settings.cohere_api_key or self.settings.cohere_api_key == "test_cohere_key":
            issues.append("Cohere API key not configured")
        
        # Check data directories
        from pathlib import Path
        data_dir = Path(self.settings.data_dir)
        if not data_dir.exists():
            issues.append(f"Data directory does not exist: {data_dir}")
        
        return issues
    
    async def _validate_dependencies(self) -> List[str]:
        """Validate external dependencies."""
        issues = []
        
        try:
            # Check Milvus connection
            from .storage.vector_store import MilvusVectorStore
            vector_store = MilvusVectorStore()
            await vector_store.initialize()
            await vector_store.close()
        except Exception as e:
            issues.append(f"Milvus connection failed: {e}")
        
        try:
            # Check Redis connection
            import redis
            r = redis.Redis(
                host=self.settings.redis_host,
                port=self.settings.redis_port,
                db=self.settings.redis_db
            )
            r.ping()
        except Exception as e:
            issues.append(f"Redis connection failed: {e}")
        
        return issues
    
    def _generate_recommendations(self, source_validation: Dict, 
                                config_issues: List[str], 
                                dependency_issues: List[str]) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []
        
        if config_issues:
            recommendations.append("Configure missing API keys in environment variables")
        
        if dependency_issues:
            recommendations.append("Ensure Milvus and Redis services are running")
        
        invalid_sources = source_validation.get("invalid_sources", [])
        if invalid_sources:
            recommendations.append(f"Fix {len(invalid_sources)} invalid source configurations")
        
        enabled_sources = source_validation.get("enabled_sources", 0)
        if enabled_sources == 0:
            recommendations.append("Enable at least one data source for processing")
        
        return recommendations
    
    def _get_final_stats(self) -> Dict[str, Any]:
        """Get final pipeline statistics."""
        duration = None
        if self.stats["start_time"] and self.stats["end_time"]:
            duration = (self.stats["end_time"] - self.stats["start_time"]).total_seconds()
        
        return {
            "success": self.stats["failed_sources"] == 0,
            "start_time": self.stats["start_time"],
            "end_time": self.stats["end_time"],
            "duration_seconds": duration,
            "total_sources": self.stats["total_sources"],
            "processed_sources": self.stats["processed_sources"],
            "failed_sources": self.stats["failed_sources"],
            "total_chunks": self.stats["total_chunks"],
            "errors": self.stats["errors"]
        }
    
    async def get_pipeline_status(self) -> Dict[str, Any]:
        """Get current pipeline status."""
        return {
            "is_running": self.is_running,
            "current_task": self.current_task,
            "stats": self.stats
        }
    
    async def stop_pipeline(self) -> bool:
        """Stop the currently running pipeline."""
        if not self.is_running:
            return False
        
        try:
            # Cancel current task if any
            if self.current_task:
                self.current_task.revoke(terminate=True)
            
            self.is_running = False
            logger.info("Pipeline stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop pipeline: {e}")
            return False


# Factory function for easy instantiation
async def create_pipeline() -> OfflinePipeline:
    """Create and initialize an offline pipeline."""
    pipeline = OfflinePipeline()
    await pipeline.initialize()
    return pipeline
