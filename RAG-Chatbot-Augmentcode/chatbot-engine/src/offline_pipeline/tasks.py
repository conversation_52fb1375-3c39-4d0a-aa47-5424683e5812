"""Celery tasks for offline pipeline processing."""

import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from celery import Celery
from celery.utils.log import get_task_logger

from ..shared.config import get_settings
from ..shared.models import DataSource, DocumentChunk, ProcessingStatus
from ..shared.utils import load_yaml_file

from .crawling.crawler import WebCrawler
from .processing.text_processor import LegalTextProcessor
from .embeddings import create_embedding_processor
from .storage.vector_store import MilvusVectorStore
from .storage.metadata_store import MetadataStore

# Initialize Celery app
settings = get_settings()
celery_app = Celery(
    'offline_pipeline',
    broker=f'redis://{settings.redis_host}:{settings.redis_port}/{settings.redis_db}',
    backend=f'redis://{settings.redis_host}:{settings.redis_port}/{settings.redis_db}'
)

# Configure Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

logger = get_task_logger(__name__)


def run_async(coro):
    """Helper to run async functions in Celery tasks."""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return loop.run_until_complete(coro)


@celery_app.task(bind=True, name='crawl_website_source')
def crawl_website_source(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Crawl a single website source.
    
    Args:
        source_data: Dictionary containing DataSource information
        
    Returns:
        Dictionary with crawl results
    """
    try:
        logger.info(f"Starting crawl task for source: {source_data.get('name')}")
        
        # Create DataSource object
        source = DataSource(**source_data)
        
        # Update task progress
        self.update_state(state='PROGRESS', meta={'status': 'Starting crawl'})
        
        async def _crawl():
            # Initialize metadata store
            metadata_store = MetadataStore()
            await metadata_store.initialize()
            
            # Update source status
            await metadata_store.update_source_status(
                source.id or source.name, 
                ProcessingStatus.PROCESSING
            )
            
            try:
                # Perform crawling
                async with WebCrawler() as crawler:
                    file_paths = await crawler.crawl_website(source)
                
                # Log success
                await metadata_store.log_processing_operation(
                    source.id or source.name,
                    "crawl",
                    "success",
                    f"Downloaded {len(file_paths)} files",
                    {"file_count": len(file_paths), "files": file_paths}
                )
                
                return {
                    "success": True,
                    "source_id": source.id or source.name,
                    "file_paths": file_paths,
                    "file_count": len(file_paths)
                }
                
            except Exception as e:
                # Log failure
                await metadata_store.log_processing_operation(
                    source.id or source.name,
                    "crawl",
                    "failed",
                    str(e)
                )
                
                await metadata_store.update_source_status(
                    source.id or source.name,
                    ProcessingStatus.FAILED
                )
                
                raise
        
        result = run_async(_crawl())
        logger.info(f"Crawl completed for {source.name}: {result['file_count']} files")
        return result
        
    except Exception as e:
        logger.error(f"Crawl task failed: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@celery_app.task(bind=True, name='process_documents')
def process_documents(self, source_id: str, file_paths: List[str]) -> Dict[str, Any]:
    """
    Process documents to extract text and create chunks.
    
    Args:
        source_id: ID of the data source
        file_paths: List of file paths to process
        
    Returns:
        Dictionary with processing results
    """
    try:
        logger.info(f"Starting document processing for source {source_id}: {len(file_paths)} files")
        
        self.update_state(state='PROGRESS', meta={'status': 'Processing documents'})
        
        async def _process():
            # Initialize components
            text_processor = LegalTextProcessor()
            metadata_store = MetadataStore()
            await metadata_store.initialize()
            
            try:
                # Process all files
                all_chunks = []
                for file_path in file_paths:
                    try:
                        chunks = text_processor.process_file(file_path, source_id)
                        all_chunks.extend(chunks)
                        logger.info(f"Processed {file_path}: {len(chunks)} chunks")
                    except Exception as e:
                        logger.error(f"Error processing {file_path}: {e}")
                
                # Log processing results
                await metadata_store.log_processing_operation(
                    source_id,
                    "text_processing",
                    "success",
                    f"Created {len(all_chunks)} chunks from {len(file_paths)} files",
                    {"chunk_count": len(all_chunks), "file_count": len(file_paths)}
                )
                
                return {
                    "success": True,
                    "source_id": source_id,
                    "chunks": [chunk.dict() for chunk in all_chunks],
                    "chunk_count": len(all_chunks)
                }
                
            except Exception as e:
                await metadata_store.log_processing_operation(
                    source_id,
                    "text_processing",
                    "failed",
                    str(e)
                )
                raise
        
        result = run_async(_process())
        logger.info(f"Document processing completed for {source_id}: {result['chunk_count']} chunks")
        return result
        
    except Exception as e:
        logger.error(f"Document processing task failed: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@celery_app.task(bind=True, name='generate_embeddings')
def generate_embeddings(self, chunks_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Generate embeddings for document chunks.
    
    Args:
        chunks_data: List of chunk dictionaries
        
    Returns:
        Dictionary with embedding results
    """
    try:
        logger.info(f"Starting embedding generation for {len(chunks_data)} chunks")
        
        self.update_state(state='PROGRESS', meta={'status': 'Generating embeddings'})
        
        async def _generate():
            # Convert dictionaries back to DocumentChunk objects
            chunks = [DocumentChunk(**chunk_data) for chunk_data in chunks_data]
            
            # Initialize embedding processor
            embedding_processor = create_embedding_processor()
            
            # Generate embeddings
            embedded_chunks = await embedding_processor.process_chunks_stream(chunks)
            
            # Validate embeddings
            validation_result = await embedding_processor.validate_embeddings(embedded_chunks)
            
            return {
                "success": True,
                "chunks": [chunk.dict() for chunk in embedded_chunks],
                "chunk_count": len(embedded_chunks),
                "validation": validation_result
            }
        
        result = run_async(_generate())
        logger.info(f"Embedding generation completed: {result['chunk_count']} chunks embedded")
        return result
        
    except Exception as e:
        logger.error(f"Embedding generation task failed: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@celery_app.task(bind=True, name='store_vectors')
def store_vectors(self, chunks_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Store document chunks with embeddings in vector database.
    
    Args:
        chunks_data: List of chunk dictionaries with embeddings
        
    Returns:
        Dictionary with storage results
    """
    try:
        logger.info(f"Starting vector storage for {len(chunks_data)} chunks")
        
        self.update_state(state='PROGRESS', meta={'status': 'Storing vectors'})
        
        async def _store():
            # Convert dictionaries back to DocumentChunk objects
            chunks = [DocumentChunk(**chunk_data) for chunk_data in chunks_data]
            
            # Initialize vector store
            vector_store = MilvusVectorStore()
            await vector_store.initialize()
            
            try:
                # Store chunks
                success = await vector_store.store_chunks(chunks)
                
                if success:
                    # Get collection stats
                    stats = await vector_store.get_collection_stats()
                    
                    return {
                        "success": True,
                        "stored_count": len(chunks),
                        "collection_stats": stats
                    }
                else:
                    raise Exception("Failed to store chunks in vector database")
                    
            finally:
                await vector_store.close()
        
        result = run_async(_store())
        logger.info(f"Vector storage completed: {result['stored_count']} chunks stored")
        return result

    except Exception as e:
        logger.error(f"Vector storage task failed: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@celery_app.task(bind=True, name='process_source_complete')
def process_source_complete(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Complete processing workflow for a data source.

    This task orchestrates the entire pipeline:
    1. Crawl website (if applicable)
    2. Process documents to create chunks
    3. Generate embeddings
    4. Store in vector database

    Args:
        source_data: Dictionary containing DataSource information

    Returns:
        Dictionary with complete processing results
    """
    try:
        logger.info(f"Starting complete processing for source: {source_data.get('name')}")

        source = DataSource(**source_data)
        source_id = source.id or source.name

        self.update_state(state='PROGRESS', meta={'status': 'Starting complete processing'})

        async def _complete_process():
            metadata_store = MetadataStore()
            await metadata_store.initialize()

            try:
                # Update source status to processing
                await metadata_store.update_source_status(source_id, ProcessingStatus.PROCESSING)

                # Step 1: Crawl if it's a website
                file_paths = []
                if source.source_type == "website":
                    crawl_result = crawl_website_source.apply_async(args=[source_data])
                    crawl_data = crawl_result.get()

                    if not crawl_data.get('success'):
                        raise Exception("Crawling failed")

                    file_paths = crawl_data['file_paths']
                else:
                    # For local files, use the path directly
                    if source.path and Path(source.path).exists():
                        file_paths = [source.path]
                    else:
                        raise Exception(f"Local file not found: {source.path}")

                # Step 2: Process documents
                process_result = process_documents.apply_async(args=[source_id, file_paths])
                process_data = process_result.get()

                if not process_data.get('success'):
                    raise Exception("Document processing failed")

                chunks_data = process_data['chunks']

                # Step 3: Generate embeddings
                embedding_result = generate_embeddings.apply_async(args=[chunks_data])
                embedding_data = embedding_result.get()

                if not embedding_data.get('success'):
                    raise Exception("Embedding generation failed")

                embedded_chunks_data = embedding_data['chunks']

                # Step 4: Store vectors
                storage_result = store_vectors.apply_async(args=[embedded_chunks_data])
                storage_data = storage_result.get()

                if not storage_data.get('success'):
                    raise Exception("Vector storage failed")

                # Update source status to completed
                await metadata_store.update_source_status(source_id, ProcessingStatus.COMPLETED)

                # Log final success
                await metadata_store.log_processing_operation(
                    source_id,
                    "complete_processing",
                    "success",
                    f"Successfully processed {len(embedded_chunks_data)} chunks",
                    {
                        "file_count": len(file_paths),
                        "chunk_count": len(embedded_chunks_data),
                        "validation": embedding_data.get('validation', {}),
                        "collection_stats": storage_data.get('collection_stats', {})
                    }
                )

                return {
                    "success": True,
                    "source_id": source_id,
                    "file_count": len(file_paths),
                    "chunk_count": len(embedded_chunks_data),
                    "validation": embedding_data.get('validation', {}),
                    "collection_stats": storage_data.get('collection_stats', {})
                }

            except Exception as e:
                # Update source status to failed
                await metadata_store.update_source_status(source_id, ProcessingStatus.FAILED)

                await metadata_store.log_processing_operation(
                    source_id,
                    "complete_processing",
                    "failed",
                    str(e)
                )

                raise

        result = run_async(_complete_process())
        logger.info(f"Complete processing finished for {source.name}: {result}")
        return result

    except Exception as e:
        logger.error(f"Complete processing task failed: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@celery_app.task(bind=True, name='process_all_sources')
def process_all_sources(self) -> Dict[str, Any]:
    """
    Process all enabled data sources from configuration.

    Returns:
        Dictionary with processing results for all sources
    """
    try:
        logger.info("Starting processing of all data sources")

        self.update_state(state='PROGRESS', meta={'status': 'Loading data sources'})

        async def _process_all():
            # Load data sources from configuration
            settings = get_settings()
            sources_config = load_yaml_file(settings.sources_config)

            if not sources_config:
                raise Exception("No sources configuration found")

            # Convert configuration to DataSource objects
            all_sources = []

            # Process website sources
            for website in sources_config.get('websites', []):
                if website.get('enabled', True):
                    source = DataSource(
                        name=website['name'],
                        source_type="website",
                        url=website['url'],
                        crawl_depth=website.get('crawl_depth', 2),
                        enabled=True
                    )
                    all_sources.append(source)

            # Process local PDF sources
            for pdf in sources_config.get('local_pdfs', []):
                if pdf.get('enabled', True):
                    source = DataSource(
                        name=pdf['name'],
                        source_type="pdf",
                        path=pdf['path'],
                        enabled=True
                    )
                    all_sources.append(source)

            logger.info(f"Found {len(all_sources)} enabled sources to process")

            # Process each source
            results = {}
            for source in all_sources:
                try:
                    logger.info(f"Processing source: {source.name}")

                    # Start complete processing for this source
                    task_result = process_source_complete.apply_async(args=[source.dict()])
                    source_result = task_result.get()

                    results[source.name] = source_result

                except Exception as e:
                    logger.error(f"Failed to process source {source.name}: {e}")
                    results[source.name] = {"success": False, "error": str(e)}

            # Calculate summary statistics
            successful_sources = len([r for r in results.values() if r.get('success')])
            total_chunks = sum(r.get('chunk_count', 0) for r in results.values() if r.get('success'))

            return {
                "success": True,
                "total_sources": len(all_sources),
                "successful_sources": successful_sources,
                "failed_sources": len(all_sources) - successful_sources,
                "total_chunks": total_chunks,
                "source_results": results
            }

        result = run_async(_process_all())
        logger.info(f"All sources processing completed: {result}")
        return result

    except Exception as e:
        logger.error(f"Process all sources task failed: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise
